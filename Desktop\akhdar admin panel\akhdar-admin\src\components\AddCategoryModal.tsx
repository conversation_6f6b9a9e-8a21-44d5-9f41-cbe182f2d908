import React, { useState } from 'react'
import { X, Upload } from 'lucide-react'
import { toast } from 'sonner'
import { useAppDispatch, useAppSelector } from '../store/hooks'
import { addCategory, fetchCategories } from '../store/slices/categoriesSlice'
import { getStorage, ref, uploadBytes, getDownloadURL } from 'firebase/storage'
import type { CategoryFormData } from '../types/category'

interface AddCategoryModalProps {
  isOpen: boolean
  onClose: () => void
}

const AddCategoryModal: React.FC<AddCategoryModalProps> = ({ isOpen, onClose }) => {
  const dispatch = useAppDispatch()
  const { isAdding } = useAppSelector((state) => state.categories)

  const [formData, setFormData] = useState<CategoryFormData>({
    title: '',
    icon: { url: '' },
    subcategories: []
  })
  const [uploadedImage, setUploadedImage] = useState<File | null>(null)
  const [previewUrl, setPreviewUrl] = useState('')
  const [isUploading, setIsUploading] = useState(false)
  const [errors, setErrors] = useState<Record<string, string>>({})
  const [subcategoryInput, setSubcategoryInput] = useState('')

  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target

    if (name === 'iconUrl') {
      setFormData({
        ...formData,
        icon: { url: value }
      })
    } else {
      setFormData({
        ...formData,
        [name]: value
      })
    }

    // Clear error when user types
    if (errors[name]) {
      setErrors({
        ...errors,
        [name]: ''
      })
    }
  }

  const handleSubcategoryInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setSubcategoryInput(e.target.value)
  }

  const addSubcategory = () => {
    if (subcategoryInput.trim()) {
      setFormData({
        ...formData,
        subcategories: [...formData.subcategories, subcategoryInput.trim()]
      })
      setSubcategoryInput('')
    }
  }

  const removeSubcategory = (index: number) => {
    setFormData({
      ...formData,
      subcategories: formData.subcategories.filter((_, i) => i !== index)
    })
  }

  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0]
    if (!file) return

    // Clear previous errors
    if (errors.icon) {
      setErrors({
        ...errors,
        icon: ''
      })
    }

    // Validate file type
    const validTypes = ['image/jpeg', 'image/png', 'image/webp']
    if (!validTypes.includes(file.type)) {
      setErrors({
        ...errors,
        icon: 'Invalid file type. Please upload a JPEG, PNG, or WEBP image.'
      })
      return
    }

    // Validate file size (max 2MB)
    if (file.size > 2 * 1024 * 1024) {
      setErrors({
        ...errors,
        icon: 'File is too large. Maximum size is 2MB.'
      })
      return
    }

    setUploadedImage(file)

    // Create preview
    const reader = new FileReader()
    reader.onloadend = () => {
      setPreviewUrl(reader.result as string)
    }
    reader.readAsDataURL(file)
  }

  const uploadIcon = async (file: File): Promise<string | null> => {
    try {
      setIsUploading(true)
      const storage = getStorage()
      const timestamp = new Date().getTime()
      const fileName = `category-icons/${timestamp}_${file.name.replace(/\s+/g, '_')}`
      const storageRef = ref(storage, fileName)

      await uploadBytes(storageRef, file)
      const downloadUrl = await getDownloadURL(storageRef)

      return downloadUrl
    } catch (error) {
      console.error('Error uploading file:', error)
      setErrors({
        ...errors,
        icon: 'Failed to upload image. Please try again.'
      })
      return null
    } finally {
      setIsUploading(false)
    }
  }

  const validateForm = (): boolean => {
    const newErrors: Record<string, string> = {}

    if (!formData.title.trim()) {
      newErrors.title = 'Title is required'
    }

    if (!uploadedImage && !formData.icon.url.trim()) {
      newErrors.icon = 'Please upload an icon or provide an icon URL'
    }

    setErrors(newErrors)
    return Object.keys(newErrors).length === 0
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()

    if (validateForm()) {
      try {
        const categoryData = { ...formData }

        // If there's an uploaded image, upload it to Firebase Storage
        if (uploadedImage) {
          const iconUrl = await uploadIcon(uploadedImage)
          if (iconUrl) {
            categoryData.icon = { url: iconUrl }
          } else {
            return // Stop if upload failed
          }
        }

        await dispatch(addCategory(categoryData)).unwrap()
        // Refetch categories to get updated subcategories
        await dispatch(fetchCategories())
        toast.success('Category added successfully')
        handleClose()
      } catch (error) {
        toast.error('Failed to add category')
        setErrors({
          ...errors,
          form: 'Failed to create category. Please try again.'
        })
      }
    }
  }

  const handleClose = () => {
    setFormData({
      title: '',
      icon: { url: '' },
      subcategories: []
    })
    setUploadedImage(null)
    setPreviewUrl('')
    setSubcategoryInput('')
    setErrors({})
    onClose()
  }

  if (!isOpen) return null

  return (
    <div className="fixed inset-0 bg-gray-100/50 flex items-center justify-center z-50 p-4">
      <div className="bg-white rounded-lg shadow-xl w-full max-w-md max-h-[90vh] overflow-y-auto">
        {/* Header */}
        <div className="flex items-center justify-between p-4 sm:p-6 border-b border-gray-200">
          <h2 className="text-lg sm:text-xl font-semibold text-gray-900">Add Category</h2>
          <button
            onClick={handleClose}
            className="text-gray-400 hover:text-gray-600 transition-colors"
          >
            <X className="w-5 h-5" />
          </button>
        </div>

        {/* Body */}
        <form onSubmit={handleSubmit} className="p-4 sm:p-6">
          {/* Title */}
          <div className="mb-4">
            <label htmlFor="title" className="flex text-sm font-medium text-gray-700 mb-2">
              Title *
            </label>
            <input
              type="text"
              id="title"
              name="title"
              value={formData.title}
              onChange={handleChange}
              className={`w-full px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 ${
                errors.title ? 'border-red-500' : 'border-gray-300'
              }`}
              placeholder="Enter category title"
            />
            {errors.title && <p className="text-red-500 text-xs mt-1">{errors.title}</p>}
          </div>

          {/* Icon Upload */}
          <div className="mb-4">
            <label className="flex text-sm font-medium text-gray-700 mb-2">
              Icon *
            </label>
            <div className="space-y-3">
              <div className="flex items-center justify-center w-full">
                <label className="flex flex-col items-center justify-center w-full h-32 border-2 border-gray-300 border-dashed rounded-lg cursor-pointer bg-gray-50 hover:bg-gray-100">
                  <div className="flex flex-col items-center justify-center pt-5 pb-6">
                    <Upload className="w-8 h-8 mb-2 text-gray-400" />
                    <p className="mb-2 text-sm text-gray-500">
                      <span className="font-semibold">Click to upload</span>
                    </p>
                    <p className="text-xs text-gray-500">PNG, JPG, WEBP (MAX. 2MB)</p>
                  </div>
                  <input
                    type="file"
                    className="hidden"
                    accept=".png,.jpg,.jpeg,.webp"
                    onChange={handleFileChange}
                  />
                </label>
              </div>

              {/* Preview */}
              {previewUrl && (
                <div className="flex justify-center">
                  <div className="relative">
                    <img
                      src={previewUrl}
                      alt="Preview"
                      className="w-20 h-20 object-cover rounded-lg border"
                    />
                  </div>
                </div>
              )}

             
            </div>
            {errors.icon && <p className="text-red-500 text-xs mt-1">{errors.icon}</p>}
          </div>

          {/* Subcategories */}
          <div className="mb-6">
            <label className="flex text-sm font-medium text-gray-700 mb-2">
              Subcategories
            </label>
            <div className="flex gap-2 mb-3">
              <input
                type="text"
                value={subcategoryInput}
                onChange={handleSubcategoryInputChange}
                onKeyDown={(e) => e.key === 'Enter' && (e.preventDefault(), addSubcategory())}
                className="flex-1 px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                placeholder="Enter subcategory"
              />
              <button
                type="button"
                onClick={addSubcategory}
                className="bg-amber-600 text-white px-4 py-2 rounded-lg hover:bg-amber-700 transition-colors text-sm font-medium"
              >
                Add
              </button>
            </div>

            {/* Subcategories Display */}
            {formData.subcategories.length > 0 && (
              <div className="mt-3">
                <p className="flex text-sm font-medium text-gray-700 mb-2">Subcategories:</p>
                <div className="flex flex-wrap gap-2">
                  {formData.subcategories.map((subcategory, index) => (
                    <span
                      key={index}
                      className="inline-flex items-center gap-1 px-3 py-1 bg-gray-100 text-gray-700 text-sm rounded-md border"
                    >
                      {subcategory}
                      <button
                        type="button"
                        onClick={() => removeSubcategory(index)}
                        className="ml-1 text-gray-500 hover:text-gray-700 transition-colors text-lg leading-none"
                      >
                        ×
                      </button>
                    </span>
                  ))}
                </div>
              </div>
            )}
          </div>

          {errors.form && <p className="text-red-500 text-sm mb-4">{errors.form}</p>}

          {/* Footer */}
          <div className="flex gap-3">
            <button
              type="button"
              onClick={handleClose}
              className="flex-1 px-4 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors"
            >
              Cancel
            </button>
            <button
              type="submit"
              disabled={isAdding || isUploading}
              className="flex-1 bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors disabled:opacity-50 disabled:cursor-not-allowed flex items-center justify-center gap-2"
            >
              {isAdding || isUploading ? (
                <>
                  <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin"></div>
                  {isUploading ? 'Uploading...' : 'Adding...'}
                </>
              ) : (
                'Add Category'
              )}
            </button>
          </div>
        </form>
      </div>
    </div>
  )
}

export default AddCategoryModal
