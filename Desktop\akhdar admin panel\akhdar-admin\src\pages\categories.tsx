import { useEffect, useState } from 'react'
import { Plus, Edit, Trash2, Eye, FolderOpen, Image } from 'lucide-react'
import { toast } from 'sonner'
import type { Category } from '../types/category'
import { useAppDispatch, useAppSelector } from '../store/hooks'
import { fetchCategories, deleteCategory } from '../store/slices/categoriesSlice'
import ConfirmationModal from '../components/ConfirmationModal'

const Categories = () => {
  const dispatch = useAppDispatch()
  const { categories, isLoading, isDeleting, error } = useAppSelector((state: any) => state.categories)
  
  const [deleteModalOpen, setDeleteModalOpen] = useState(false)
  const [categoryToDelete, setCategoryToDelete] = useState<string | null>(null)
  const [detailsModalOpen, setDetailsModalOpen] = useState(false)
  const [selectedCategory, setSelectedCategory] = useState<Category | null>(null)

  useEffect(() => {
    dispatch(fetchCategories())
  }, [dispatch])

  console.log("categories",categories)

  useEffect(() => {
    if (error) {
      toast.error(error)
    }
  }, [error])

  // Get only parent categories (categories without parentCategory or parentCategoryId)
  const parentCategories = categories.filter((cat: any) => !cat.parentCategory && !cat.parentCategoryId)

  const handleDeleteClick = (categoryId: string) => {
    setCategoryToDelete(categoryId)
    setDeleteModalOpen(true)
  }

  const handleEditClick = (_category: Category) => {
    // TODO: Implement edit modal
    toast.info('Edit functionality coming soon')
  }

  const handleViewDetails = (category: Category) => {
    setSelectedCategory(category)
    setDetailsModalOpen(true)
  }

  const confirmDelete = async () => {
    if (categoryToDelete) {
      try {
        await dispatch(deleteCategory(categoryToDelete)).unwrap()
        toast.success('Category deleted successfully')
        setDeleteModalOpen(false)
        setCategoryToDelete(null)
      } catch (error) {
        toast.error('Failed to delete category')
      }
    }
  }

  const handleCloseDeleteModal = () => {
    setDeleteModalOpen(false)
    setCategoryToDelete(null)
  }

  const handleCloseDetailsModal = () => {
    setDetailsModalOpen(false)
    setSelectedCategory(null)
  }

  // Get subcategories for a parent category
  const getSubcategories = (parentId: string) => {
    return categories.filter((cat: any) =>
      cat.parentCategory === parentId || cat.parentCategoryId === parentId
    )
  }

  const getSubcategoriesCount = (categoryId: string) => {
    return getSubcategories(categoryId).length
  }

  if (isLoading) {
    return (
      <div className="p-6">
        <div className="flex items-center justify-center h-64">
          <div className="w-8 h-8 border-4 border-blue-600 border-t-transparent rounded-full animate-spin"></div>
        </div>
      </div>
    )
  }

  return (
    <div className="p-4 sm:p-6">
      {/* Header */}
      <div className="mb-6 sm:mb-8 flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
        <div>
          <h1 className="text-xl sm:text-2xl font-bold text-gray-900 mb-2">Categories</h1>
          <p className="text-sm sm:text-base text-gray-600">
            Manage your product categories and subcategories.
          </p>
        </div>
        <button
          onClick={() => toast.info('Add category functionality coming soon')}
          className="bg-blue-600 hover:bg-blue-700 text-white px-3 sm:px-4 py-2 rounded-lg transition-colors flex items-center gap-2 text-sm sm:text-base w-full sm:w-auto justify-center"
        >
          <Plus className="w-4 h-4 sm:w-5 sm:h-5" />
          Add Category
        </button>
      </div>

      {/* Categories Table */}
      <div className="bg-white rounded-xl shadow-sm border border-gray-200 overflow-hidden">
        {parentCategories.length === 0 ? (
          <div className="text-center py-16">
            <div className="w-20 h-20 mx-auto mb-6 bg-gradient-to-br from-blue-100 to-blue-200 rounded-full flex items-center justify-center">
              <FolderOpen className="w-8 h-8 text-blue-500" />
            </div>
            <h3 className="text-xl font-semibold text-gray-900 mb-3">
              No categories found
            </h3>
            <p className="text-gray-600 max-w-md mx-auto">
              There are no categories in the system yet. Start by adding your first category to get started.
            </p>
          </div>
        ) : (
          <div className="overflow-x-auto">
            <table className="min-w-full divide-y divide-gray-200">
              <thead className="bg-gradient-to-r from-gray-50 to-gray-100">
                <tr>
                  <th className="px-3 sm:px-6 py-4 text-left text-xs font-bold text-gray-700 uppercase tracking-wider">
                    #
                  </th>
                  <th className="px-3 sm:px-6 py-4 text-left text-xs font-bold text-gray-700 uppercase tracking-wider">
                    Icon
                  </th>
                  <th className="px-3 sm:px-6 py-4 text-left text-xs font-bold text-gray-700 uppercase tracking-wider pl-26">
                    Title
                  </th>
                  <th className="hidden sm:table-cell px-3 sm:px-6 py-4 text-left text-xs font-bold text-gray-700 uppercase tracking-wider pl-26">
                    Subcategories
                  </th>
                  <th className="px-3 sm:px-6 py-4 text-center text-xs font-bold text-gray-700 uppercase tracking-wider">
                    Actions
                  </th>
                </tr>
              </thead>
              <tbody className="bg-white divide-y divide-gray-100">
                {parentCategories.map((category: Category, index: number) => (
                  <tr key={category.id} className="hover:bg-blue-50 transition-colors duration-150">
                    <td className="px-3 sm:px-6 py-4 sm:py-5 whitespace-nowrap">
                      <div className="w-6 h-6 sm:w-8 sm:h-8 bg-blue-100 rounded-full flex items-center justify-center">
                        <span className="text-xs sm:text-sm font-bold text-blue-600">
                          {index + 1}
                        </span>
                      </div>
                    </td>
                    <td className="px-3 sm:px-6 py-4 sm:py-5 whitespace-nowrap">
                      {(category.icon?.url || (category as any).icon?.url) ? (
                        <img
                          src={category.icon?.url || (category as any).icon?.url}
                          alt={category.title}
                          className="w-8 h-8 sm:w-10 sm:h-10 rounded-lg object-cover"
                          onError={(e) => {
                            const target = e.target as HTMLImageElement;
                            target.style.display = 'none';
                            target.nextElementSibling?.classList.remove('hidden');
                          }}
                        />
                      ) : null}
                      <div className={`w-8 h-8 sm:w-10 sm:h-10 bg-gray-200 rounded-lg flex items-center justify-center ${(category.icon?.url || (category as any).icon?.url) ? 'hidden' : ''}`}>
                        <Image className="w-4 h-4 sm:w-5 sm:h-5 text-gray-400" />
                      </div>
                    </td>
                    <td className="px-3 sm:px-6 py-4 sm:py-5">
                      <div className="text-sm font-semibold text-gray-900">
                        {category.title}
                      </div>
                      <div className="sm:hidden mt-1">
                        <span className="inline-flex px-2 py-1 text-xs font-bold rounded-full bg-blue-100 text-blue-800">
                          {getSubcategoriesCount(category.id)} subcategories
                        </span>
                      </div>
                    </td>
                    <td className="hidden sm:table-cell px-3 sm:px-6 py-4 sm:py-5 whitespace-nowrap">
                      <span className="inline-flex px-3 py-1 text-xs font-bold rounded-full bg-blue-100 text-blue-800">
                        {getSubcategoriesCount(category.id)}
                      </span>
                    </td>
                    <td className="px-3 sm:px-6 py-4 sm:py-5 whitespace-nowrap text-center">
                      <div className="flex items-center justify-center space-x-1 sm:space-x-2">
                        <button
                          onClick={() => handleViewDetails(category)}
                          className="bg-green-100 text-green-600 hover:bg-green-200 hover:text-green-700 p-1.5 sm:p-2 rounded-lg transition-all duration-200 shadow-sm hover:shadow-md"
                          title="View Details"
                        >
                          <Eye className="w-3 h-3 sm:w-4 sm:h-4" />
                        </button>
                        <button
                          onClick={() => handleEditClick(category)}
                          className="bg-blue-100 text-blue-600 hover:bg-blue-200 hover:text-blue-700 p-1.5 sm:p-2 rounded-lg transition-all duration-200 shadow-sm hover:shadow-md"
                          title="Edit"
                        >
                          <Edit className="w-3 h-3 sm:w-4 sm:h-4" />
                        </button>
                        <button
                          onClick={() => handleDeleteClick(category.id)}
                          className="bg-red-100 text-red-600 hover:bg-red-200 hover:text-red-700 p-1.5 sm:p-2 rounded-lg transition-all duration-200 shadow-sm hover:shadow-md"
                          title="Delete"
                        >
                          <Trash2 className="w-3 h-3 sm:w-4 sm:h-4" />
                        </button>
                      </div>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        )}
      </div>

      {/* Delete Confirmation Modal */}
      <ConfirmationModal
        isOpen={deleteModalOpen}
        onClose={handleCloseDeleteModal}
        onConfirm={confirmDelete}
        title="Delete Category"
        subtitle="Are you sure you want to delete this category? This action will also delete all subcategories and cannot be undone."
        confirmLabel="Delete"
        confirmColor="danger"
        isLoading={isDeleting}
      />

      {/* Category Details Modal */}
      {detailsModalOpen && selectedCategory && (
        <div className="fixed inset-0  flex items-center justify-center z-50 p-4">
          <div className="bg-white rounded-lg shadow-xl w-full max-w-2xl max-h-[90vh] overflow-y-auto">
            {/* Header */}
            <div className="flex items-center justify-between p-4 sm:p-6 border-b border-gray-200">
              <div className="flex items-center space-x-3">
                {(selectedCategory.icon?.url || (selectedCategory as any).icon?.url) && (
                  <img
                    src={selectedCategory.icon?.url || (selectedCategory as any).icon?.url}
                    alt={selectedCategory.title}
                    className="w-6 h-6 sm:w-8 sm:h-8 rounded-lg object-cover"
                    onError={(e) => {
                      const target = e.target as HTMLImageElement;
                      target.style.display = 'none';
                    }}
                  />
                )}
                <h2 className="text-lg sm:text-xl font-semibold text-gray-900">{selectedCategory.title}</h2>
              </div>
              <button
                onClick={handleCloseDetailsModal}
                className="text-gray-400 hover:text-gray-600 transition-colors text-xl sm:text-2xl"
              >
                ×
              </button>
            </div>

            {/* Body */}
            <div className="p-4 sm:p-6">
              {(() => {
                const subcategories = getSubcategories(selectedCategory.id);
                return (
                  <>
                    <h3 className="text-base sm:text-lg font-semibold text-gray-900 mb-4">
                      Subcategories ({subcategories.length})
                    </h3>

                    {subcategories.length > 0 ? (
                      <div className="grid grid-cols-1 sm:grid-cols-2 gap-3">
                        {subcategories.map((subcategory: any) => (
                          <div key={subcategory.id} className="flex items-center space-x-3 p-3 bg-gray-50 rounded-lg">
                            {(subcategory.icon?.url || subcategory.icon?.url) ? (
                              <img
                                src={subcategory.icon?.url || subcategory.icon?.url}
                                alt={subcategory.title}
                                className="w-6 h-6 sm:w-8 sm:h-8 rounded-lg object-cover flex-shrink-0"
                                onError={(e) => {
                                  const target = e.target as HTMLImageElement;
                                  target.style.display = 'none';
                                  target.nextElementSibling?.classList.remove('hidden');
                                }}
                              />
                            ) : null}
                            <div className={`w-6 h-6 sm:w-8 sm:h-8 bg-gray-200 rounded-lg flex items-center justify-center flex-shrink-0 ${(subcategory.icon?.url || subcategory.icon?.url) ? 'hidden' : ''}`}>
                              <Image className="w-3 h-3 sm:w-4 sm:h-4 text-gray-400" />
                            </div>
                            <span className="text-xs sm:text-sm font-medium text-gray-900 truncate">{subcategory.title}</span>
                          </div>
                        ))}
                      </div>
                    ) : (
                      <p className="text-gray-500 text-center py-8 text-sm sm:text-base">No subcategories found</p>
                    )}
                  </>
                );
              })()}
            </div>
          </div>
        </div>
      )}
    </div>
  )
}

export default Categories
